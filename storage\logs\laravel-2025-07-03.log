[2025-07-03 15:01:35] local.ERROR: Using $this when not in object context {"exception":"[object] (Error(code: 0): Using $this when not in object context at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:239)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Filesystem\\Filesystem::{closure}(Object(Illuminate\\Foundation\\Application))
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 {main}
"} 
[2025-07-03 15:01:35] local.ERROR: Using $this when not in object context {"exception":"[object] (Error(code: 0): Using $this when not in object context at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:239)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Filesystem\\Filesystem::{closure}(Object(Illuminate\\Foundation\\Application))
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 {main}
"} 
[2025-07-03 15:06:35] local.ERROR: syntax error, unexpected token "," {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \",\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:1031)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-03 15:06:35] local.ERROR: syntax error, unexpected token "," {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \",\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:1031)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-03 15:39:01] local.INFO: {"namespace":["The namespace field is required."]}  
